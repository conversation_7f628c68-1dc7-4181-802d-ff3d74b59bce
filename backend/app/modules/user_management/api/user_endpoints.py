# user_management/api/user_endpoints.py - 用户管理API端点
"""
用户管理API端点

功能：
1. 用户列表查询
2. 用户详情查看
3. 用户信息更新
4. 用户状态管理（激活/禁用/封禁）
5. 用户角色管理
6. 用户搜索
"""

import logging
from typing import Any, Dict, List

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session

from app.core.schemas import BaseResponse, PaginationResponse
from app.modules.user_management.models.auth_models import AuthingUser
from database.connection import get_db_session

from ..models.schemas import (
    AssignRoleRequest,
    AuthingUserSchema,
    UserListQuery,
    UserProfileSchema,
    UserUpdateRequest,
)
from ..services.role_service import RoleService
from ..services.user_service import UserService
from .dependencies import (
    get_current_active_user,
    require_admin,
    require_user_admin,
    require_user_read,
    require_user_write,
)

logger = logging.getLogger(__name__)

user_router = APIRouter()


@user_router.get(
    "/",
    response_model=BaseResponse[PaginationResponse[UserProfileSchema]],
    summary="获取用户列表",
    description="分页获取用户列表，支持关键词搜索和状态筛选",
)
async def list_users(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    keyword: str = Query(None, description="搜索关键词"),
    role: str = Query(None, description="角色筛选"),
    is_active: bool = Query(None, description="状态筛选"),
    sort_by: str = Query("created_at", description="排序字段"),
    sort_order: str = Query("desc", regex="^(asc|desc)$", description="排序方向"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_read),
):
    """获取用户列表"""
    try:
        query = UserListQuery(
            page=page,
            page_size=page_size,
            keyword=keyword,
            role=role,
            is_active=is_active,
            sort_by=sort_by,
            sort_order=sort_order,
        )

        user_service = UserService()
        users, total = user_service.list_users(db, query)

        # 转换为响应模式
        user_profiles = [UserProfileSchema.from_orm(user) for user in users]

        pagination_data = PaginationResponse.create(
            items=user_profiles, total=total, page=page, page_size=page_size
        )

        return BaseResponse(
            success=True, message="获取用户列表成功", data=pagination_data
        )

    except Exception as e:
        logger.error(f"获取用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户列表失败"
        )


@user_router.get(
    "/{user_id}",
    response_model=BaseResponse[UserProfileSchema],
    summary="获取用户详情",
    description="根据用户ID获取用户详细信息",
)
async def get_user(
    user_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_read),
):
    """获取用户详情"""
    try:
        user_service = UserService()
        user = user_service.get_user_by_id(db, user_id)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        return BaseResponse(
            success=True,
            message="获取用户详情成功",
            data=UserProfileSchema.from_orm(user),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户详情失败"
        )


@user_router.put(
    "/{user_id}",
    response_model=BaseResponse[UserProfileSchema],
    summary="更新用户信息",
    description="更新指定用户的信息",
)
async def update_user(
    user_id: int,
    request: UserUpdateRequest,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_write),
):
    """更新用户信息"""
    try:
        user_service = UserService()

        # 检查用户是否存在
        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        # 更新用户信息
        updated_user = user_service.update_user_profile(db, user_id, request)

        if not updated_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="更新用户信息失败"
            )

        return BaseResponse(
            success=True,
            message="用户信息更新成功",
            data=UserProfileSchema.from_orm(updated_user),
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新用户信息失败"
        )


@user_router.post(
    "/{user_id}/activate",
    response_model=BaseResponse[Dict[str, Any]],
    summary="激活用户",
    description="激活指定用户账户",
)
async def activate_user(
    user_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_admin),
):
    """激活用户"""
    try:
        user_service = UserService()
        success = user_service.activate_user(db, user_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="激活用户失败"
            )

        return BaseResponse(
            success=True, message="用户激活成功", data={"user_id": user_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"激活用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="激活用户失败"
        )


@user_router.post(
    "/{user_id}/block",
    response_model=BaseResponse[Dict[str, Any]],
    summary="封禁用户",
    description="封禁指定用户账户",
)
async def block_user(
    user_id: int,
    reason: str = Query(..., description="封禁原因"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_admin),
):
    """封禁用户"""
    try:
        user_service = UserService()
        success = user_service.block_user(db, user_id, reason)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="封禁用户失败"
            )

        return BaseResponse(
            success=True,
            message="用户封禁成功",
            data={"user_id": user_id, "reason": reason},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"封禁用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="封禁用户失败"
        )


@user_router.delete(
    "/{user_id}",
    response_model=BaseResponse[Dict[str, Any]],
    summary="删除用户",
    description="软删除指定用户（设置为非激活状态）",
)
async def delete_user(
    user_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_admin),
):
    """删除用户（软删除）"""
    try:
        # 防止删除自己
        if user_id == current_user.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="不能删除自己的账户"
            )

        user_service = UserService()
        success = user_service.delete_user(db, user_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="删除用户失败"
            )

        return BaseResponse(
            success=True, message="用户删除成功", data={"user_id": user_id}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除用户失败"
        )


@user_router.get(
    "/{user_id}/roles",
    response_model=BaseResponse[List[str]],
    summary="获取用户角色",
    description="获取指定用户的角色列表",
)
async def get_user_roles(
    user_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_read),
):
    """获取用户角色"""
    try:
        role_service = RoleService()
        roles = role_service.get_user_roles(db, user_id)

        role_codes = [role.code for role in roles]

        return BaseResponse(success=True, message="获取用户角色成功", data=role_codes)

    except Exception as e:
        logger.error(f"获取用户角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户角色失败"
        )


@user_router.post(
    "/{user_id}/roles",
    response_model=BaseResponse[Dict[str, Any]],
    summary="分配用户角色",
    description="为指定用户分配角色",
)
async def assign_user_roles(
    user_id: int,
    request: AssignRoleRequest,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_admin),
):
    """分配用户角色"""
    try:
        # 检查用户是否存在
        user_service = UserService()
        user = user_service.get_user_by_id(db, user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="用户不存在"
            )

        # 更新请求中的用户ID
        request.user_id = user_id

        role_service = RoleService()
        success = role_service.assign_roles_to_user(db, request, current_user.id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="分配用户角色失败"
            )

        return BaseResponse(
            success=True,
            message="用户角色分配成功",
            data={"user_id": user_id, "role_ids": request.role_ids},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分配用户角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="分配用户角色失败"
        )


@user_router.delete(
    "/{user_id}/roles/{role_id}",
    response_model=BaseResponse[Dict[str, Any]],
    summary="移除用户角色",
    description="从用户移除指定角色",
)
async def remove_user_role(
    user_id: int,
    role_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_admin),
):
    """移除用户角色"""
    try:
        role_service = RoleService()
        success = role_service.remove_role_from_user(db, user_id, role_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="移除用户角色失败"
            )

        return BaseResponse(
            success=True,
            message="用户角色移除成功",
            data={"user_id": user_id, "role_id": role_id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"移除用户角色失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="移除用户角色失败"
        )


@user_router.get(
    "/{user_id}/permissions",
    response_model=BaseResponse[List[str]],
    summary="获取用户权限",
    description="获取指定用户的权限列表",
)
async def get_user_permissions(
    user_id: int,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_read),
):
    """获取用户权限"""
    try:
        from ..services.permission_service import PermissionService

        permission_service = PermissionService()
        permissions = permission_service.get_user_permissions(db, user_id)

        permission_codes = [perm.code for perm in permissions]

        return BaseResponse(
            success=True, message="获取用户权限成功", data=permission_codes
        )

    except Exception as e:
        logger.error(f"获取用户权限失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户权限失败"
        )


@user_router.get(
    "/{user_id}/sessions",
    response_model=BaseResponse[List[Dict[str, Any]]],
    summary="获取用户会话",
    description="获取指定用户的活跃会话列表",
)
async def get_user_sessions(
    user_id: int,
    active_only: bool = Query(True, description="是否只显示活跃会话"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_read),
):
    """获取用户会话"""
    try:
        user_service = UserService()
        sessions = user_service.get_user_sessions(db, user_id, active_only)

        session_data = []
        for session in sessions:
            session_data.append(
                {
                    "session_id": session.session_id,
                    "device_info": session.device_info,
                    "login_ip": session.login_ip,
                    "login_location": session.login_location,
                    "login_at": (
                        session.login_at.isoformat() if session.login_at else None
                    ),
                    "last_activity_at": (
                        session.last_activity_at.isoformat()
                        if session.last_activity_at
                        else None
                    ),
                    "expires_at": (
                        session.expires_at.isoformat() if session.expires_at else None
                    ),
                    "is_active": session.is_active,
                }
            )

        return BaseResponse(success=True, message="获取用户会话成功", data=session_data)

    except Exception as e:
        logger.error(f"获取用户会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取用户会话失败"
        )


@user_router.delete(
    "/{user_id}/sessions/{session_id}",
    response_model=BaseResponse[Dict[str, Any]],
    summary="终止用户会话",
    description="终止指定用户的特定会话",
)
async def terminate_user_session(
    user_id: int,
    session_id: str,
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_admin),
):
    """终止用户会话"""
    try:
        user_service = UserService()
        success = user_service.terminate_session(db, session_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, detail="终止会话失败"
            )

        return BaseResponse(
            success=True,
            message="会话终止成功",
            data={"user_id": user_id, "session_id": session_id},
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"终止用户会话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="终止用户会话失败"
        )


@user_router.get(
    "/search",
    response_model=BaseResponse[List[AuthingUserSchema]],
    summary="搜索用户",
    description="根据关键词搜索用户",
)
async def search_users(
    keyword: str = Query(..., min_length=2, description="搜索关键词"),
    limit: int = Query(10, ge=1, le=50, description="返回结果数量限制"),
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_user_read),
):
    """搜索用户"""
    try:
        user_service = UserService()
        users = user_service.search_users(db, keyword, limit)

        user_schemas = [AuthingUserSchema.from_orm(user) for user in users]

        return BaseResponse(success=True, message="用户搜索成功", data=user_schemas)

    except Exception as e:
        logger.error(f"搜索用户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="搜索用户失败"
        )


@user_router.get(
    "/stats",
    response_model=BaseResponse[Dict[str, Any]],
    summary="获取用户统计信息",
    description="获取用户相关的统计数据",
)
async def get_user_stats(
    db: Session = Depends(get_db_session),
    current_user: AuthingUser = Depends(require_admin),
):
    """获取用户统计信息"""
    try:
        user_service = UserService()
        stats = user_service.get_user_stats(db)

        return BaseResponse(success=True, message="获取用户统计信息成功", data=stats)

    except Exception as e:
        logger.error(f"获取用户统计信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取用户统计信息失败",
        )
